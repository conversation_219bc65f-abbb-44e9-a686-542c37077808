import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Home from './src/pages/Home';
import Settings from './src/pages/Settings';
import { RootStack } from './src/types';

const Stack = createNativeStackNavigator<RootStack>();

function App() {
  return (
    <NavigationContainer>
      <SafeAreaProvider>
        <StatusBar
          barStyle="dark-content"
          translucent={false}
          animated={true}
        />
        <Stack.Navigator
          initialRouteName="Home"
          screenOptions={{ headerShown: false }}
        >
          <Stack.Screen name="Home" component={Home} />
          <Stack.Screen name="settings" component={Settings} />
        </Stack.Navigator>
      </SafeAreaProvider>
    </NavigationContainer>
  );
}

export default App;
