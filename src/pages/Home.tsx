import { View, ActivityIndicator, StyleSheet, Alert } from 'react-native';
import { useDevicePermissions } from '../hooks/useDevicePermissions';
import CheckPermission from '../components/CheckPermission';
import LocationCard from '../components/LocationCard';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ShutterButton from '../components/ShutterButton';
import { useIsForeground } from '../hooks/useIsForeground.ts';
import { RouteProp, useIsFocused, useNavigation } from '@react-navigation/core';
import { Camera, useCameraDevice, VideoFile } from 'react-native-vision-camera';
import { useMemo, useRef, useState } from 'react';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import CameraHeader from '../components/CameraHeader';
import { RootStack } from '../types/index';


type ChatProps = RouteProp<RootStack, 'settings'>;

export default function Home() {
  const {
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
    isLoading,
  } = useDevicePermissions();

  const { bottom } = useSafeAreaInsets();
  const [flashMode, setFlashMode] = useState<'on' | 'off'>('off');

  const [cameraPosition, setCameraPosition] = useState<'back' | 'front'>(
    'back',
  );

  const [enableLocation, setEnableLocation] = useState(false);

  const [recording, setRecording] = useState(false);

  const isFocussed = useIsFocused();

  const isForeground = useIsForeground();

  const isActive = isFocussed && isForeground;

  const device = useCameraDevice(cameraPosition);

  const cameraRef = useRef<Camera>(null);

  const { navigate } = useNavigation();

  async function onRecordingFinished(video: VideoFile) {
    try {
      const asset = await CameraRoll.saveAsset(video.path, {
        type: 'video',
        album: 'vCam',
      });
      Alert.alert('Success', `Video saved to ${asset.node.image.filepath}`);
    } catch (error) {
      console.error(error);
      Alert.alert('Error', 'Failed to save video to gallery.');
    }
    setRecording(false);
  }

  function startRecording() {
    setRecording(true);
    cameraRef.current?.startRecording({
      onRecordingFinished: onRecordingFinished,
      flash: flashMode,
      onRecordingError: error => {
        console.error('Recording error: ', error);
      },
    });
  }

  function stopRecording() {
    cameraRef.current?.stopRecording();
  }

  const videoFormat = useMemo(() => {
    if (device?.formats == null) return undefined;

    const sevenTwentyPFormats = device.formats.filter(
      f => f.videoHeight === 720 && f.videoWidth === 1280,
    );

    if (sevenTwentyPFormats.length === 0) return undefined;

    return sevenTwentyPFormats.reduce((prev, curr) => {
      return curr.maxFps > prev.maxFps ? curr : prev;
    });
  }, []);

  function toggleCamera() {
    setCameraPosition(prev => (prev === 'back' ? 'front' : 'back'));
  }

  function toggleLocation() {
    setEnableLocation(prev => !prev);
  }

  function toggleFlash() {
    setFlashMode(prev => (prev === 'on' ? 'off' : 'on'));
  }

  function navigateToSettings() {
    navigate('settings');
  }

  if (isLoading) {
    return (
      <ActivityIndicator
        size="large"
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
      />
    );
  }

  if (
    [
      audioPermission,
      cameraPermission,
      storagePermission,
      locationPermission,
    ].some(permission => permission !== 'granted')
  ) {
    return <CheckPermission />;
  }

  return (
    <>
      <CameraHeader
        enableLocation={enableLocation}
        toggleLocation={toggleLocation}
        toggleCamera={toggleCamera}
        toggleFlash={toggleFlash}
        flashMode={flashMode}
        cameraPosition={cameraPosition}
        hasFlash={device?.hasFlash}
        recording={recording}
        navigateToSettings={navigateToSettings}
      />
      {device && (
        <Camera
          device={device}
          isActive={isActive}
          style={StyleSheet.absoluteFill}
          photo={true}
          video={true}
          audio={true}
          videoBitRate={2.3}
          ref={cameraRef}
          lowLightBoost={device.supportsLowLightBoost}
          outputOrientation="device"
          pixelFormat="yuv"
          format={videoFormat}
          enableLocation={enableLocation}
        />
      )}
      <View style={{ flex: 1 }}>
        <View
          style={{
            position: 'absolute',
            left: 8,
            right: 8,
            bottom: bottom + 12,
            gap: 12,
            paddingHorizontal: 8,
          }}
        >
          {enableLocation && <LocationCard />}
          <ShutterButton
            onStartRecording={startRecording}
            onStopRecording={stopRecording}
            recording={recording}
          />
        </View>
      </View>
    </>
  );
}
