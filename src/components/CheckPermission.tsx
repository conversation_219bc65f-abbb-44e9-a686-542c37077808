import { StyleSheet, View, Text } from 'react-native';
import { useDevicePermissions } from '../hooks/useDevicePermissions';

export default function CheckPermission() {
  const {
    audioPermission,
    locationPermission,
    storagePermission,
    cameraPermission,
    requestCameraPermission,
    requestAudioPermission,
    requestLocationPermission,
    requestStoragePermission,
  } = useDevicePermissions();

  return (
    <View style={styles.container}>
      <Text style={styles.welcome}>Welcome to V Cam.</Text>
      <View style={styles.permissionsContainer}>
        {cameraPermission !== 'granted' && (
          <Text style={styles.permissionText}>
            V <PERSON> needs <Text style={styles.bold}>Camera permission</Text>.{' '}
            <Text style={styles.hyperlink} onPress={requestCameraPermission}>
              Grant
            </Text>
          </Text>
        )}

        {audioPermission !== 'granted' && (
          <Text style={styles.permissionText}>
            V Cam needs <Text style={styles.bold}>Microphone permission </Text>
            <Text style={styles.hyperlink} onPress={requestAudioPermission}>
              Grant
            </Text>
          </Text>
        )}

        {locationPermission !== 'granted' && (
          <Text style={styles.permissionText}>
            V Cam needs <Text style={styles.bold}>Location permission </Text>
            <Text style={styles.hyperlink} onPress={requestLocationPermission}>
              Grant
            </Text>
          </Text>
        )}

        {storagePermission !== 'granted' && (
          <Text style={styles.permissionText}>
            V Cam needs <Text style={styles.bold}>storage permission </Text>
            <Text style={styles.hyperlink} onPress={requestStoragePermission}>
              Grant
            </Text>
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  welcome: {
    fontSize: 36,
    fontWeight: 'bold',
  },

  container: {
    flex: 1,
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  permissionsContainer: {
    marginTop: 12,
    gap: 12,
  },
  permissionText: {
    fontSize: 17,
  },
  hyperlink: {
    color: '#007aff',
    fontWeight: 'bold',
  },
  bold: {
    fontWeight: 'bold',
  },
});
