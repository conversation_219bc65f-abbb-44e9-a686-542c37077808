import React, { useState, useRef } from 'react';
import { Pressable, Text, View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  cancelAnimation,
  withRepeat,
  withTiming,
  Easing,
  withSequence,
} from 'react-native-reanimated';

interface ShutterButtonProps {
  onStartRecording?: () => void;
  onStopRecording?: () => void;
  size?: number;
  color?: string;
  recording: boolean;
}

export default function ShutterButton({
  onStartRecording,
  onStopRecording,
  size = 80,
  color = '#ffffff',
  recording,
}: ShutterButtonProps) {
  const scale = useSharedValue(1);
  const [seconds, setSeconds] = useState(0);
  const intervalRef = useRef<number | null>(null);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));
  const redDotOpacity = useSharedValue(1);

  const redDotStyle = useAnimatedStyle(() => ({
    opacity: redDotOpacity.value,
  }));

  const toggleRecording = () => {
    if (recording) {
      onStopRecording?.();

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      scale.value = withSpring(1);
      cancelAnimation(redDotOpacity);
      redDotOpacity.value = 1;
    } else {
      setSeconds(0);
      onStartRecording?.();

      intervalRef.current = setInterval(() => {
        setSeconds(prev => prev + 1);
      }, 1000);

      scale.value = withSpring(0.85);

      redDotOpacity.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 500, easing: Easing.linear }),
          withTiming(0, { duration: 500, easing: Easing.linear }),
        ),
        -1,
        false,
      );
    }
  };

  return (
    <View style={{ alignItems: 'center' }}>
      {recording && (
        <View style={styles.timerContainer}>
          <Animated.View style={[styles.redDot, redDotStyle]} />
          <Text style={styles.timerText}>
            REC{' '}
            {Math.floor(seconds / 60)
              .toString()
              .padStart(2, '0')}
            :{(seconds % 60).toString().padStart(2, '0')}
          </Text>
        </View>
      )}
      <Pressable onPress={toggleRecording}>
        <Animated.View
          style={[
            styles.outerCircle,
            animatedStyle,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              borderColor: color,
              backgroundColor: recording ? '#ff3b30' : 'transparent',
            },
          ]}
        >
          <Animated.View
            style={{
              width: size * 0.6,
              height: size * 0.6,
              borderRadius: (size * 0.6) / 2,
              backgroundColor: color,
            }}
          />
        </Animated.View>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  outerCircle: {
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff3b30',
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  redDot: {
    width: 10,
    height: 10,
    borderRadius: 10,
    backgroundColor: '#ff3b30',
  },
});
