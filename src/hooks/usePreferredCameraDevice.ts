import { useState, useEffect, useCallback, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCameraDevices, CameraDevice } from 'react-native-vision-camera';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';

export function usePreferredCameraDevice(): [
  CameraDevice | undefined,
  (device: CameraDevice) => Promise<void>,
] {
  const [preferredDeviceId, setPreferredDeviceId] = useState<string | null>(
    null,
  );

  useEffect(() => {
    AsyncStorage.getItem(STORAGE_KEYS.PREFERREDDEVICE).then(storedId => {
      if (storedId) setPreferredDeviceId(storedId);
    });
  }, []);

  const set = useCallback(async (device: CameraDevice) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.PREFERREDDEVICE, device.id);
      setPreferredDeviceId(device.id);
    } catch (e) {
      console.error('Failed to save preferred camera device', e);
    }
  }, []);

  const devices = useCameraDevices();
  const device = useMemo(
    () => devices.find(d => d.id === preferredDeviceId),
    [],
  );

  return [device, set];
}
