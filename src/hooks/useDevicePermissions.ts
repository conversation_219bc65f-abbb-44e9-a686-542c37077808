import { useEffect, useState, useCallback } from 'react';
import {
  PERMISSIONS,
  check,
  checkMultiple,
  request,
  requestMultiple,
} from 'react-native-permissions';
import { PermissionState, PermissionStatus } from '../types';
import {
  loadPermissionsFromStorage,
  savePermissionsToStorage,
} from '../utils/helper';
import { Linking, Platform } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

const defaultPermissions: PermissionState = {
  camera: 'denied',
  audio: 'denied',
  location: 'denied',
  storage: 'denied',
};

export function useDevicePermissions() {
  const [permissions, setPermissions] =
    useState<PermissionState>(defaultPermissions);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStoredPermissions = async () => {
      const storedPermissions = await loadPermissionsFromStorage();
      if (storedPermissions) {
        setPermissions(storedPermissions);
      }
      setIsLoading(false);
    };
    loadStoredPermissions();
  }, []);

  useFocusEffect(() => {
    if (!isLoading) {
      checkMultiplePermissions();
    }
  });

  const requestMultiplePermissions = useCallback(async () => {
    const response = await requestMultiple([
      PERMISSIONS.ANDROID.CAMERA,
      PERMISSIONS.ANDROID.RECORD_AUDIO,
      PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
      PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
    ]);

    const newPermissions = {
      camera: response[PERMISSIONS.ANDROID.CAMERA] as PermissionStatus,
      audio: response[PERMISSIONS.ANDROID.RECORD_AUDIO] as PermissionStatus,
      location: response[
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
      ] as PermissionStatus,
      storage: response[
        PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
      ] as PermissionStatus,
    };

    setPermissions(newPermissions);
    savePermissionsToStorage(newPermissions);
  }, []);

  const checkMultiplePermissions = useCallback(async () => {
    const response = await checkMultiple([
      PERMISSIONS.ANDROID.CAMERA,
      PERMISSIONS.ANDROID.RECORD_AUDIO,
      PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
    ]);

    const newPermissions = {
      camera: response[PERMISSIONS.ANDROID.CAMERA] as PermissionStatus,
      audio: response[PERMISSIONS.ANDROID.RECORD_AUDIO] as PermissionStatus,
      location: response[
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
      ] as PermissionStatus,
      storage: response[
        PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE
      ] as PermissionStatus,
    };

    setPermissions(newPermissions);
  }, []);

  const requestLocationPermission = useCallback(async () => {
    const response = await requestMultiple([
      PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
    ]);

    if (response[PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION] === 'blocked') {
      Linking.openSettings();
    }

    if (response[PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION] === 'granted') {
      setPermissions(prev => ({
        ...prev,
        location: response[
          PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
        ] as PermissionStatus,
      }));
    }
  }, []);

  const checkLocationPermission = useCallback(async () => {
    const response = await checkMultiple([
      PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
    ]);

    setPermissions(prev => ({
      ...prev,
      location: response[
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
      ] as PermissionStatus,
    }));
  }, []);

  const requestCameraPermission = useCallback(async () => {
    const response = await request(PERMISSIONS.ANDROID.CAMERA);
    setPermissions(prev => ({
      ...prev,
      camera: response as PermissionStatus,
    }));
  }, []);

  const checkCameraPermission = useCallback(async () => {
    const response = await check(PERMISSIONS.ANDROID.CAMERA);
    setPermissions(prev => ({
      ...prev,
      camera: response as PermissionStatus,
    }));
  }, []);

  const requestAudioPermission = useCallback(async () => {
    const response = await request(PERMISSIONS.ANDROID.RECORD_AUDIO);
    setPermissions(prev => ({
      ...prev,
      audio: response as PermissionStatus,
    }));
  }, []);

  const checkAudioPermission = useCallback(async () => {
    const response = await check(PERMISSIONS.ANDROID.RECORD_AUDIO);
    setPermissions(prev => ({
      ...prev,
      audio: response as PermissionStatus,
    }));
  }, []);

  const requestStoragePermission = useCallback(async () => {
    try {
      const response =
        Number(Platform.Version) >= 33
          ? await request(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES)
          : await request(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE);

      console.log(response, 'response');
      if (response === 'blocked') {
        Linking.openSettings();
      }
      setPermissions(prev => ({
        ...prev,
        storage: response as PermissionStatus,
      }));
    } catch (error) {
      console.log(error, 'error');
    }
  }, []);

  const checkStoragePermission = useCallback(async () => {
    const response = await check(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES);
    setPermissions(prev => ({
      ...prev,
      storage: response as PermissionStatus,
    }));
  }, []);

  return {
    permissions,
    isLoading,
    cameraPermission: permissions.camera,
    audioPermission: permissions.audio,
    locationPermission: permissions.location,
    storagePermission: permissions.storage,
    requestMultiplePermissions,
    checkMultiplePermissions,
    requestLocationPermission,
    checkLocationPermission,
    requestCameraPermission,
    checkCameraPermission,
    requestAudioPermission,
    checkAudioPermission,
    requestStoragePermission,
    checkStoragePermission,
  };
}
