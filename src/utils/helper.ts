import AsyncStorage from '@react-native-async-storage/async-storage';
import { PermissionState } from '../types';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';
// import type { Frame } from 'react-native-vision-camera'
import { Frame, VisionCameraProxy } from 'react-native-vision-camera';

export const savePermissionsToStorage = async (
  permissions: PermissionState,
): Promise<void> => {
  try {
    await AsyncStorage.setItem(
      STORAGE_KEYS.PERMISSIONS,
      JSON.stringify(permissions),
    );
  } catch (error) {
    console.error('Error saving permissions to storage:', error);
  }
};

export const loadPermissionsFromStorage =
  async (): Promise<PermissionState | null> => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.PERMISSIONS);
      if (stored) {
        return JSON.parse(stored) as PermissionState;
      }
      return null;
    } catch (error) {
      console.error('Error parsing stored permissions:', error);
      return null;
    }
  };

export const getCurrentTimestamp = () => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  return new Date().toLocaleString('en-US', options);
};

const plugin = VisionCameraProxy.initFrameProcessorPlugin('example_plugin', {});

interface Result {
  example_array: (string | number | boolean)[];
  example_array_buffer: ArrayBuffer;
  example_str: string;
  example_bool: boolean;
  example_double: number;
}

export function examplePlugin(frame: Frame): Result {
  'worklet';

  if (plugin == null)
    throw new Error('Failed to load Frame Processor Plugin "example_plugin"!');

  return plugin.call(frame, {
    someString: 'hello!',
    someBoolean: true,
    someNumber: 42,
    someObject: { test: 0, second: 'test' },
    someArray: ['another test', 5],
  }) as unknown as Result;
}
