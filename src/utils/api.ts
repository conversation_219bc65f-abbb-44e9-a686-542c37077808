import { AddressResponseType } from '../types';

export async function getAddress(
  lat: number,
  lon: number,
): Promise<AddressResponseType> {
  const response = await fetch(
    `https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=json`,
    {
      headers: {
        'Content-Type': 'application/json',
        'user-agent': 'vCam/1.0',
      },
    },
  );
  const data = await response.json();
  return data;
}
