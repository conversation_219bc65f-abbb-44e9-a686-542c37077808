{"name": "vCam", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android && react-native start", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-camera-roll/camera-roll": "^7.10.2", "@react-native-vector-icons/common": "^12.3.0", "@react-native-vector-icons/ionicons": "^12.3.0", "@react-native/new-app-screen": "0.81.4", "@react-navigation/native-stack": "^7.3.27", "@react-navigation/stack": "^7.4.9", "react": "19.1.0", "react-native": "0.81.4", "react-native-compressor": "^1.13.0", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.28.0", "react-native-mmkv": "^3.3.3", "react-native-permissions": "^5.4.2", "react-native-reanimated": "^4.1.2", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-vision-camera": "^4.7.2", "react-native-worklets": "^0.6.0", "react-native-worklets-core": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.4", "@react-native/eslint-config": "0.81.4", "@react-native/metro-config": "0.81.4", "@react-native/typescript-config": "0.81.4", "@types/jest": "^29.5.13", "@types/react": "^19.2.0", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.9.3"}, "engines": {"node": ">=20"}}